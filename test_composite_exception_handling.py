#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试复合实验异常收集功能的有效性

作者：KangKang Geng
测试目标：
1. 验证装饰器自动应用到重写的方法
2. 验证异常处理机制的正确性
3. 验证不需要修改子类代码即可获得异常处理能力
"""

import asyncio
import sys
import os
import copy
from copy import deepcopy
from unittest.mock import Mock

print("开始测试复合实验异常收集功能...")

# 简化的测试实现
import functools
import copy


def composite_exception_handler(func):
    """简化版装饰器用于测试"""

    @functools.wraps(func)
    async def wrapper(self, *args, **kwargs):
        print(f"装饰器被调用，包装方法: {func.__name__}")

        # 包装 deepcopy 函数
        original_deepcopy = copy.deepcopy

        def wrapped_deepcopy(obj, *deepcopy_args, **deepcopy_kwargs):
            result = original_deepcopy(obj, *deepcopy_args, **deepcopy_kwargs)
            print(f"deepcopy 被调用，对象类型: {type(result)}")
            if hasattr(result, "run_experiment") and hasattr(result, "EXP_TYPE"):
                print(f"发现子实验对象，准备包装: {result}")
                if not hasattr(result, "_exception_wrapped"):
                    print("开始包装子实验的 run_experiment 方法")
                    original_run = result.run_experiment

                    async def wrapped_run(*run_args, **run_kwargs):
                        try:
                            print("包装的 run_experiment 被调用")
                            return await original_run(*run_args, **run_kwargs)
                        except Exception as e:
                            print(f"装饰器捕获异常: {e}")
                            self._handle_child_experiment_exception(result, e)
                            return None

                    result.run_experiment = wrapped_run
                    result._exception_wrapped = True
                    print("子实验包装完成")
                else:
                    print("子实验已经被包装过")
            return result

        copy.deepcopy = wrapped_deepcopy

        try:
            return await func(self, *args, **kwargs)
        finally:
            copy.deepcopy = original_deepcopy

    return wrapper


class MockTopExperiment:
    """模拟子实验类"""

    EXP_TYPE = "TOP"

    def __init__(self):
        self.analysis = Mock()
        self.status = Mock()
        self.run_options = Mock()
        self.run_options.index = 0

    async def run_experiment(self, **kwargs):
        """模拟实验执行，可能抛出异常"""
        if hasattr(self, "_should_fail") and self._should_fail:
            raise RuntimeError("模拟子实验执行失败")
        print("子实验执行成功")
        return "success"


class CompositeExperiment:
    """简化版 CompositeExperiment 用于测试"""

    _sub_experiment_class = MockTopExperiment

    def __init__(self):
        self._child_experiment = self._sub_experiment_class()
        self.exception_count = 0  # 用于测试的异常计数器
        self._experiments = []

    def __init_subclass__(cls, **kwargs):
        """自动应用装饰器"""
        super().__init_subclass__(**kwargs)
        print(f"__init_subclass__ 被调用，处理类: {cls.__name__}")

        methods_to_wrap = ["_sync_composite_run", "_async_composite_run_base"]

        for method_name in methods_to_wrap:
            if hasattr(cls, method_name):
                base_method = getattr(CompositeExperiment, method_name, None)
                subclass_method = getattr(cls, method_name)

                print(
                    f"检查方法 {method_name}: base={base_method}, subclass={subclass_method}"
                )

                if base_method is not None and subclass_method is not base_method:
                    if not hasattr(subclass_method, "_exception_handler_applied"):
                        print(f"为方法 {method_name} 应用装饰器")
                        wrapped_method = composite_exception_handler(subclass_method)
                        wrapped_method._exception_handler_applied = True
                        setattr(cls, method_name, wrapped_method)
                    else:
                        print(f"方法 {method_name} 已经被装饰过")
                else:
                    print(f"方法 {method_name} 不需要装饰")

    def _handle_child_experiment_exception(self, child_exp, exception):
        """处理子实验异常"""
        self.exception_count += 1
        print(f"✓ 基类捕获到子实验异常: {type(exception).__name__}: {exception}")

    def _add_child_experiment(self, exp):
        self._experiments.append(exp)

    async def _sync_composite_run(self):
        """基类的同步运行方法"""
        print("执行基类的 _sync_composite_run")


# 创建测试子类
class TestCompositeExperiment(CompositeExperiment):
    """测试子类，重写了运行方法"""

    async def _sync_composite_run(self):
        """重写的同步运行方法，没有异常处理"""
        print("执行子类重写的 _sync_composite_run（应该自动获得异常处理）")

        for i in range(3):
            print(f"创建第 {i+1} 个子实验")
            child_exp = copy.deepcopy(self._child_experiment)
            if i >= 1:  # 后两个实验设置为失败
                child_exp._should_fail = True
                print(f"第 {i+1} 个子实验设置为失败")

            # 直接调用 run_experiment，没有异常处理
            print(f"执行第 {i+1} 个子实验")
            try:
                await child_exp.run_experiment()
                self._add_child_experiment(child_exp)
                print(f"第 {i+1} 个子实验处理完成")
            except Exception as e:
                print(f"第 {i+1} 个子实验执行失败，但没有被装饰器捕获: {e}")
                # 这里应该不会执行到，因为装饰器应该捕获异常


async def test_exception_handling():
    """测试异常处理功能"""
    print("=" * 60)
    print("测试复合实验异常收集功能")
    print("=" * 60)

    # 测试：子类自动获得异常处理
    print("\n测试子类自动获得异常处理:")
    test_exp = TestCompositeExperiment()

    try:
        await test_exp._sync_composite_run()
        print(f"子类捕获异常数量: {test_exp.exception_count}")
        print(f"子类实验列表长度: {len(test_exp._experiments)}")

        if test_exp.exception_count > 0:
            print("✓ 子类成功继承了异常处理能力！")
        else:
            print("✗ 子类没有捕获到异常，异常处理可能失效")

    except Exception as e:
        print(f"✗ 子类执行失败: {e}")
        import traceback

        traceback.print_exc()

    # 验证装饰器应用
    print("\n验证装饰器自动应用:")
    method = getattr(TestCompositeExperiment, "_sync_composite_run")
    if hasattr(method, "_exception_handler_applied"):
        print("✓ 装饰器已自动应用到子类方法")
    else:
        print("✗ 装饰器未应用到子类方法")

    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(test_exception_handling())
