# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/05
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    import os
    import sys

    app_path = os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    )
    print(f"load app path {app_path}")
    sys.path.append(app_path)

    from app.tool.regression_test.worker import FakeExperimentExecutor

    worker = FakeExperimentExecutor(
        config_path="/home/<USER>/code/pyqcat-apps/.settings/config/s251/nix/config.conf",
        is_record=True,
    )
    # worker.regression_test_long_time()
    worker.regression_test_from_ids("683929af13e00dd6f6166b0b")
