# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/06/30
# __author:       <PERSON><PERSON><PERSON> Shi
"""
Base Experiment class.
"""

import json
from collections import defaultdict
from copy import deepcopy
from threading import Thread
from typing import TYPE_CHECKING, Dict, List, Optional, Union

import numpy as np

from ..analysis.algorithms import IQdiscriminator
from ..analysis.standard_curve_analysis import StandardCurveAnalysis
from ..analysis.top_analysis import TopAnalysis
from ..errors import (
    AnalysisOptionsError,
    ChildExperimentError,
    CourierError,
    ExperimentAnalysisTypeError,
    ExperimentContextError,
    ExperimentOptionsError,
    RunOptionsError,
)
from ..executor.structures import ExperimentContext
from ..instrument import Instrument
from ..invoker import DataCenter
from ..log import pyqlog
from ..pulse import PulseCorrection
from ..qm_protocol import EnvironmentBitResource
from ..qubit import BaseQubitsType, Coupler, Qubit, QubitPair
from ..structures import MetaData, Options, QDict
from ..tools.record_structure import ExperimentRecordMetaData, BatchRecordMetaData
from ..tools.savefile import BaseFile, create_file
from ..tools.serialization import PyQCatEncoder
from ..tools.template_options import TemplateOptions
from ..tools.utilities import rocket_optimize
from ..tools.app_data_collector import AppDataCollector
from ..types import ExperimentType, Status, SimulatorMode

if TYPE_CHECKING:
    from ..config import PyqcatConfig


class UnitMapMixin:
    unit_keys = [
        "qubit",
        "coupler",
        "qubit_pair",
        "probeQ",
        "driveQ",
        "qh",
        "ql",
        "qc",
        "target_unit",
        "bias_unit",
    ]

    def inject_property(self):
        for prop_name in self.unit_keys:
            self._add_property(prop_name)

    def _add_property(self, prop_name):
        def getter(self):
            if (
                self.run_options
                and self.run_options.unit_map
                and prop_name in self.run_options.unit_map
            ):
                return getattr(self.run_options.unit_map, prop_name)
            else:
                return None

        def setter(self, value):
            setattr(self.run_options.unit_map, prop_name, value)

        setattr(self.__class__, prop_name, property(getter, setter))


class BaseExperiment(UnitMapMixin):
    """Abstract base class for experiments."""

    EXP_TYPE = ExperimentType.BASE

    def __init__(
        self,
        inst: Optional[Instrument] = None,
        qubits: Optional[Union[Qubit, List[Qubit]]] = None,
        couplers: Optional[Union[Coupler, List[Coupler]]] = None,
        qubit_pair: Optional[Union[Coupler, List[QubitPair]]] = None,
        compensates: Optional[Dict[BaseQubitsType, PulseCorrection]] = None,
        discriminators: Optional[Union[IQdiscriminator, List[IQdiscriminator]]] = None,
        working_dc: Optional[Dict[str, List]] = None,
        ac_bias: Optional[Dict[str, List]] = None,
        is_paternal: bool = True,
        config: Optional["PyqcatConfig"] = None,
        env_bit_resource: Optional[EnvironmentBitResource] = None,
    ):
        """Initialize the experiment object."""
        # Experiment Status
        self.status: Status = Status()

        # Experiment instrument
        self.inst = deepcopy(inst)

        # Experimental dc voltage working environment
        self.working_dc = working_dc or {}

        # 2023/3/20 72bit online:
        # Fixed unknown bug: The read fidelity increases significantly
        # as the AC prepare time increases, and does not converge until
        # the AC prepare time is about 120 us.
        self.ac_bias = deepcopy(ac_bias) or {}

        # Experimental Z-line related line compensator
        self.compensates = compensates or {}

        # Experiment reading related IQ returned data classifier
        self.discriminator = discriminators

        # Experiment dependent qubits
        if qubits is None:
            self.qubits: List = []
        elif isinstance(qubits, List):
            self.qubits = qubits
        else:
            self.qubits = [qubits]

        # Experiment dependent couplers
        if couplers is None:
            self.couplers = []
        elif isinstance(couplers, List):
            self.couplers = couplers
        else:
            self.couplers = [couplers]

        # Experiment dependent couplers
        if qubit_pair is None:
            self.qubit_pairs = []
        elif isinstance(qubit_pair, List):
            self.qubit_pairs = qubit_pair
        else:
            self.qubit_pairs = [qubit_pair]

        # Experiment register unique identifier
        self.task_id = None

        # Experimental process data recording tool
        self.file = None

        # Analysis section
        self._analysis = None  # instance of class `TopAnalysis`

        # Experiment options
        self._experiment_options = self._default_experiment_options()

        # Run experiment middle operate options
        self._run_options = self._default_run_options()

        # Analysis options
        self._analysis_options = self._default_analysis_options()

        # Experiment label. The current class name is used by default
        self._label = self.__class__.__name__

        # Coupler basic characterization experimental label
        self.is_coupler_exp = False

        # experiment_data  - > ExperimentData
        self._experiment_data = None

        self.is_paternal = is_paternal
        self.paternal_id = ""
        self.config = config if config else QDict()

        # file describe
        self.file_describe = None

        # dir describe
        self.dir_describe = None

        # env bit resource
        self.env_bit_resource = env_bit_resource

        # dynamic inject property
        self.inject_property()

        if self.EXP_TYPE == ExperimentType.BATCH:
            self.record_meta = BatchRecordMetaData.create()
        else:
            self.record_meta = ExperimentRecordMetaData.create()

    def __repr__(self) -> str:
        qubit_str = self.get_qubit_str()
        parent_label = self.run_options.parent_label or None
        label = self._label or self.__class__.__name__
        description = self.run_options.description or None

        if self.run_options.parent_process_bar:
            label = f"{label}({self.run_options.parent_process_bar})"

        names = [v for v in [qubit_str, parent_label, label, description] if v]
        return "-".join(names)

    def __deepcopy__(self, memodict: Dict = None):
        """Overwrite deepcopy."""
        if memodict is None:
            memodict = {}
        special_fields = ["compensates", "ac_bias", "env_bit_resource", "context_meta"]

        cls = self.__class__
        result = cls.__new__(cls)
        memodict[id(self)] = result
        for k, v in self.__dict__.items():
            if k in special_fields:
                setattr(result, k, v)
            else:
                setattr(result, k, deepcopy(v, memodict))

        result.record_meta = ExperimentRecordMetaData.create()

        return result

    @property
    def record_id(self):
        return self.record_meta.record_id

    @property
    def id(self):
        return self.record_meta.record_id

    @property
    def label(self):
        return self._label

    @property
    def analysis(self) -> Union[TopAnalysis, None]:
        """Return the analysis instance for the experiment"""
        if not self._analysis:
            self._analysis = StandardCurveAnalysis.empty_analysis()
        return self._analysis

    @analysis.setter
    def analysis(self, analysis: Union[TopAnalysis, None]) -> None:
        """Set the analysis instance for the experiment"""
        if analysis is not None and not isinstance(analysis, TopAnalysis):
            raise ExperimentAnalysisTypeError(
                self, f"analysis must be TopAnalysis subclass, but now it's {analysis}"
            )
        self._analysis = analysis

    @property
    def experiment_options(self) -> Options:
        """Return the options for the experiment."""
        return self._experiment_options

    @property
    def run_options(self) -> Options:
        """Return options values for the experiment :meth:`run` method."""
        return self._run_options

    @property
    def analysis_options(self) -> Options:
        """Return the options for the experiment."""
        return self._analysis_options

    @property
    def experiment_data(self):
        if self.analysis:
            return self.analysis.experiment_data
        return self._experiment_data

    @property
    def parent_label(self):
        parent_label = self.run_options.parent_label or None
        label = self._label or self.__class__.__name__
        description = self.run_options.description or None

        if self.run_options.parent_process_bar:
            label = f"{label}({self.run_options.parent_process_bar})"

        names = [v for v in [parent_label, label, description] if v]
        return "-".join(names)

    @property
    def simulator_mode(self) -> SimulatorMode:
        use_simulator = self.experiment_options.use_simulator
        simulator_data_path = self.experiment_options.simulator_data_path
        if use_simulator and simulator_data_path:
            return SimulatorMode.EXP
        elif use_simulator:
            return SimulatorMode.FLOW
        return SimulatorMode.CLOSE

    @classmethod
    def from_experiment_context(cls, context: ExperimentContext) -> "BaseExperiment":
        """Create experiment using `ExperimentContext` instance.

        Args:
            context (ExperimentContext or ExpContext): The instance of `ExperimentContext`.

        Returns:
            `BaseExperiment` object.
        """
        if not context.context_name:
            pyqlog.warning("No environment name defined in ExperimentContext!")

        exp = cls(
            inst=context.inst,
            qubits=context.qubits,
            couplers=context.couplers,
            qubit_pair=context.qubit_pair,
            compensates=context.compensates,
            discriminators=context.discriminators,
            working_dc=context.working_dc,
            ac_bias=context.ac_bias,
            config=context.config,
        )

        # feature: zyc 2024/12/02: Replace `env_bit_dict` in run options with `env_bit_resource`
        exp.env_bit_resource = context.env_bit_resource

        # feature: zyc 2024/05/14: Context type validate
        # refactor: zyc 2024/11/26: Optimize the verification process
        support_context = exp.run_options.support_context
        if not support_context:
            pyqlog.warning(f"No limit environment name defined in `{cls.__name__}`!")
        elif context.context_name not in support_context:
            raise ExperimentContextError(
                f"The `ExperimentContext` does not match `{cls.__name__}`:\n"
                f"Context: {context.context_name}\n"
                f"Supported contexts: {support_context}"
            )

        def recursive_set_options(_exp, **kwargs):
            _exp.set_run_options(**kwargs)
            if hasattr(_exp, "child_experiment"):
                child_exp = getattr(_exp, "child_experiment")
                child_exp.env_bit_resource = context.env_bit_resource
                recursive_set_options(child_exp, **kwargs)

        recursive_set_options(
            exp,
            config=context.config,
            crosstalk_dict=context.crosstalk_dict,
            online_bits=context.online_bits,
            online_dcms=context.online_dcms,
            online_coms=context.online_coms,
            xy_crosstalk_dict=context.xy_crosstalk_dict,
            # env_bit_dict=context.env_bit_dict,
            read_env_bits=context.read_env_bits or [],
            unit_map=context.unit_map,
            token=context.token,
            context_name=context.context_name,
        )
        return exp

    @classmethod
    def _default_analysis_options(cls) -> Options:
        """Default kwarg options for analysis."""
        # Experiment subclasses should override this method to return
        # an `Options` object containing all the supported options for
        # that experiment and their default values. Only options listed
        # here can be modified later by the different methods for
        # setting options.
        options = Options()

        options.set_validator("is_plot", bool)
        options.set_validator("figsize", tuple)
        options.set_validator("raw_data_format", ["scatter", "plot"])

        options.is_plot = True
        options.figsize = (12, 8)
        options.raw_data_format = "scatter"
        options.result_name = None

        # feature: Expansion of Pure Experimental Mode
        options.set_validator("pure_exp_mode", bool)
        options.pure_exp_mode = False

        options.set_validator("save_s3", bool)
        options.save_s3 = True

        options.set_validator("save_exp_data", bool)
        options.save_exp_data = True

        return options

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default kwarg options for experiment."""
        # Experiment subclasses should override this method to return
        # an `Options` object containing all the supported options for
        # that experiment and their default values. Only options listed
        # here can be modified later by the different methods for
        # setting options.

        # The show_result, display analysis result or not.

        # The config, crosstalk_dict parameters value,
        # normal provide by context object corresponding property.
        # The sample and env_name mark chip sample and simulator environment name,
        # when save file to ConfigStore, use the parameter

        options = Options()

        # options.set_validator("show_result", bool)
        options.set_validator("save_result", bool)
        options.set_validator("simulator_data_path", str)
        # options.set_validator("simulator_remote_path", str)
        options.set_validator("record_text", bool)
        options.set_validator("save_context", bool)

        # options.set_validator("time_perform", bool)
        # Not recommended modification simulator shape
        # options.set_validator('simulator_shape', list)
        # options.set_validator("use_simulator", bool)

        # options.show_result = True
        options.save_result = True
        options.simulator_shape = None
        options.simulator_data_path = None
        # options.simulator_remote_path = None
        options.record_text = True
        options.use_simulator = False
        options.save_context = True
        # options.time_perform = True

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default options values for the experiment :meth:`run` method."""
        options = Options()

        options.config = None
        options.crosstalk_dict = None
        options.simulator_data_path = None
        options.parent_process_bar = None

        options.use_simulator = False
        # CompositeExperiment child index; normal experiment default to 0.
        options.simulator_index = 0
        # CompositeExperiment`child will be set parent`_label; normal experiment default to self._label
        options.simulator_name = ""

        # Only parallel is useful.
        # removed from: https://document.qpanda.cn/docs/L9kBMzaZ7ph9gQqK
        # options.is_divide_bf = False
        # options.is_divide_bus = False
        options.use_union_rd = False

        # Only online context is useful
        options.online_bits = None
        options.online_dcms = None
        options.online_coms = None

        # xy crosstalk
        options.xy_crosstalk_dict = {}
        # options.env_bit_dict = {}

        # use to add readout point env
        options.read_env_bits = []

        options.token = ""

        # Shq 2024/04/10
        # for async mode.
        # List or numpy array.
        options.x_data = None
        options.analysis_class = None

        # run options for parallel options
        # Add parallel parameter, mark experiment is or not parallel run.
        options.parallel = False
        options.parallel_token = None
        options.is_parallel_send = False
        # options.run_mode = "sync"
        options.parallel_count = QDict(index=1, flow_count=1)
        options.parent_label = None
        options.description = None
        options.child_index = None
        # options.parent_id = None

        # validation context type
        options.support_context = None

        # Custom Unit Description, use to control result file
        options.custom_unit_describe = ""

        # Whether to collect qubit str
        options.collect_qubits = True

        # experiment context unit property map
        options.unit_map = None

        # record experiment context name
        options.context_name = ""

        return options

    @classmethod
    def template_options(cls):
        experiment_options = cls._default_experiment_options()
        analysis_options = cls._default_analysis_options()
        experiment_options.pop("validator")
        analysis_options.pop("validator")
        run_options = cls._default_run_options()
        context = run_options.support_context

        template = TemplateOptions()
        template.meta.exp_class_name == cls.__name__
        template.context_options.name = context[0] if context else ""
        template.options_for_regular_exec.experiment_options = (
            experiment_options.to_dict()
        )
        template.options_for_regular_exec.analysis_options = analysis_options.to_dict()

        return template

    def set_analysis_options(self, **fields):
        """Set the analysis options.

        Args:
            fields: The fields to update the options

        Raises:
            AnalysisOptionsError: If the field passed in is not a supported options
        """
        # for field in fields:
        #     if field not in self._analysis_options:
        #         raise AnalysisOptionsError(
        #             self._label,
        #             key=field,
        #             value=fields.get(field),
        #             msg=f"field {field} option must be defined in advance!",
        #         )
        self._analysis_options.update(**fields)

    def set_experiment_options(self, **fields):
        """Set the experiment options.

        Args:
            fields: The fields to update the options

        Raises:
            ExperimentOptionsError: If the field passed in is not a supported options
        """
        # for field in fields:
        #     if field not in self._experiment_options and not field.startswith(
        #         "schedule"
        #     ):
        #         raise ExperimentOptionsError(
        #             self._label,
        #             key=field,
        #             value=fields.get(field),
        #             msg=f"field {field} option must be defined in advance!",
        #         )
        self._experiment_options.update(**fields)
        # hook to set PulseComponent attribute fake.
        # Insure call this method before pulse generation.
        # PulseComponent.fake = self._experiment_options.fake_pulse

    def set_run_options(self, **fields):
        """Set options values for the experiment  :meth:`run` method.

        Args:
            fields: The fields to update the options

        Raises:
            RunOptionsError: If the field passed in is not a supported options
        """
        # for field in fields:
        #     if field not in self._run_options:
        #         raise RunOptionsError(
        #             self._label,
        #             key=field,
        #             value=fields.get(field),
        #             msg=f"field {field} option must be defined in advance!",
        #         )
        self._run_options.update(**fields)

    def get_qubit_str(self):
        """Get qubit str, an args qubit for SaveFile class.

        Returns:
            qubit_str: string, use to create save data path.
        """
        if self.run_options.custom_unit_describe:
            return self.run_options.custom_unit_describe

        if self.qubit_pair:
            return self.qubit_pair.name

        if self.coupler:
            return self.coupler.name

        if self.qubit:
            return self.qubit.name

        info_list = []
        q_name_list = list(set(qubit.name for qubit in self.qubits))
        c_name_list = list(set(coupler.name for coupler in self.couplers))

        info_list.extend(sorted(q_name_list))
        info_list.extend(sorted(c_name_list))
        return "".join(info_list)

    def get_option_value(self, field: str, mode: str = "exp"):
        if mode == "exp":
            options = self.experiment_options
        elif mode == "ana":
            options = self.analysis_options
        else:
            options = self.run_options

        while options and field not in options:
            if mode == "exp":
                options = options.child_exp_options
            elif mode == "ana":
                options = options.child_ana_options
            else:
                options = None

        if options:
            return options[field]

    def set_parent_file(
        self,
        parent_exp,
        description: str = "None",
        index: Optional[int] = None,
        total: Optional[Union[int, str]] = None,
    ):
        """Set parent file saved object.

        Args:
            parent_exp (BaseExperiment): BaseExperiment class object.
            description (str): The description of the file.
            index (int): child exp index.
            total (int, str): child exp run total.
        """
        if parent_exp._experiment_data and index < len(parent_exp._experiment_data):
            self._experiment_data = parent_exp._experiment_data[index]

        save_file = parent_exp.file
        index_ = int(index) + 1 if index is not None else "unknow"
        total = total or "unknow"
        parent_process_bar = f"{index_}-{total}"
        self.run_options.parent_process_bar = parent_process_bar

        new_file = deepcopy(save_file)
        task = self._label
        new_file.file_extension(
            task,
            parent_process_bar,
            description,
            parent_exp.experiment_options.is_sub_merge,
        )
        self.file = new_file
        self.run_options.parallel_count = parent_exp.run_options.parallel_count
        self.run_options.parent_label = parent_exp.parent_label
        self.run_options.description = description
        self.run_options.child_index = index_

        # feature 2024/06/04: Used to stop asynchronous composite experiments
        # self.run_options.parent_id = parent_exp.record_id
        self.record_meta.execute_meta.parent_id = parent_exp.record_id
        self.record_meta.execute_meta.parallel_id = (
            parent_exp.record_meta.execute_meta.parallel_id
        )
        self.record_meta.execute_meta.batch_id = (
            parent_exp.record_meta.execute_meta.batch_id
        )
        if not parent_exp.record_meta.execute_meta.child_record_ids:
            parent_exp.record_meta.execute_meta.child_record_ids = []
        parent_exp.record_meta.execute_meta.child_record_ids.append(self.record_id)

    def options_table(self, mode: Optional[str] = None, detail: bool = False):
        data = defaultdict(list)

        if mode is None or mode in "experiment":
            for key, value in self.experiment_options.items():
                if not detail and key in [
                    "bind_dc",
                    "bind_drive",
                    "bind_probe",
                    "multi_readout_channels",
                    "data_type",
                    "enable_one_sweep",
                    "measure_bits",
                    "loop_num",
                    "crosstalk_dict",
                    "iq_flag",
                    "file_flag",
                ]:
                    continue
                if key == "crosstalk_dict":
                    value = "has crosstalk" if value else "no crosstalk"
                if key != "validator":
                    data["type"].append("experiment option")
                    data["name"].append(key)
                    data["value"].append(value)

        if mode is None or mode in "analysis":
            for key, value in self.analysis_options.items():
                if key != "validator":
                    data["type"].append("analysis option")
                    data["name"].append(key)
                    data["value"].append(value)

        return data

    def add_execute_exp_note(self, exp_type, extra):
        # Check the save_context option and skip data writes when False
        if not self.experiment_options.save_context:
            pyqlog.debug(f"Skip database write for {self._label} (save_context=False)")
            return
        try:
            db = DataCenter()
            simulate = bool(self.simulator_mode)
            data = db.execute_exp(
                exp_id=str(self.id),
                exp_name=self._label,
                exp_type=exp_type,
                chimera_data=self.run_options.chimera_data,
                extra=extra,
                is_paternal=self.is_paternal,
                paternal_id=self.paternal_id,
                is_simulate=simulate,
                is_parallel=self.run_options.parallel,
                index=self.run_options.parallel_count.index,
            )
            if data.get("code") != 200:
                raise CourierError("save execute record error", data)
        except Exception as err:
            pyqlog.error(str(err))

    def update_execute_exp(self, status: int = 1):
        # Check the save_context option and skip data writes when False
        if not self.experiment_options.save_context:
            pyqlog.debug(f"Skip database write for {self._label} (save_context=False)")
            return

        if not self.run_options.parent_label:
            try:
                db = DataCenter()
                if self.analysis and self.analysis.quality:
                    quality_obj = self.analysis.quality
                    if isinstance(quality_obj, dict):
                        quality_obj = list(quality_obj.values())[0]

                    if isinstance(quality_obj, str):
                        quality, quality_des = quality_obj, quality_obj
                    else:
                        quality, quality_des = quality_obj.value, quality_obj.descriptor
                else:
                    quality, quality_des = 0, "error"
                    status = 2 if status == 1 else status
                    if status == 3:
                        quality_des = "stop"
                data = db.update_execute_exp(
                    exp_id=str(self.id),
                    status=status,
                    quality=quality,
                    quality_des=quality_des,
                    index=self.run_options.parallel_count.index,
                )
                if data.get("code") != 200:
                    raise CourierError("update execute state error", data)
            except Exception as err:
                pyqlog.error(str(err))

    def physical_units_map(self):
        physical_units = {}
        for qubit in self.qubits:
            physical_units[qubit.name] = qubit
        for coupler in self.couplers:
            physical_units[coupler.name] = coupler
        for pair in self.qubit_pairs:
            physical_units[pair.name] = pair
        return physical_units

    def cache_options(self):
        def _filter_validator_options(_data):
            if isinstance(_data, dict):
                if "validator" in _data:
                    _data.pop("validator")

                for _v in _data.values():
                    _filter_validator_options(_v)

        def _check_d(da):
            if isinstance(da, np.ndarray) or isinstance(da, np.matrix):
                return str(da)

            if isinstance(da, Dict):
                # bug fix 2024/02/01: not allowed to modify original data
                new_da = {}
                for k, v in da.items():
                    new_da[k] = _check_d(v)
                return new_da

            if isinstance(da, List):
                return [_check_d(_d) for _d in da]

            return da

        def _record_dcm():
            dcm_infos = {}
            if self.discriminator:
                if isinstance(self.discriminator, IQdiscriminator):
                    dcm_infos[self.discriminator.name] = self.discriminator.to_dict()
                elif isinstance(self.discriminator, List):
                    for dcm in self.discriminator:
                        dcm_infos[dcm.name] = dcm.to_dict()
            return dcm_infos

        exp_options = deepcopy(self.experiment_options)
        ana_options = deepcopy(self.analysis_options)
        _filter_validator_options(exp_options)
        _filter_validator_options(ana_options)

        return {
            str(self): {
                "exp": _check_d(exp_options),
                "ana": _check_d(ana_options),
                "dcm": _record_dcm(),
            }
        }

    async def run_experiment(self):
        pass

    async def callback_for_error(self):
        pass

    def _metadata(self) -> MetaData:
        """Return experiment metadata for ExperimentData.

        Subclasses can override this method to add custom experiment
        metadata to the returned experiment result data.
        """
        q_bits = None
        c_bits = None
        for qubit in self.qubits:
            if q_bits is None:
                q_bits = []
            q_bits.append(qubit.name)

        for coupler in self.couplers:
            if c_bits is None:
                c_bits = []
            c_bits.append(coupler.name)

        metadata = MetaData(
            name=str(self),
            qubits=q_bits,
            couplers=c_bits,
            save_location=self.file.dirs if self.file else None,
            exp_class_name=type(self).__name__,
        )

        if self.run_options.custom_unit_describe:
            metadata.process_meta["custom_unit_describe"] = (
                self.run_options.custom_unit_describe
            )

        # send record_text to metadata
        metadata.process_meta["record_text"] = self.experiment_options.record_text

        return metadata

    def _config_file_sys(self):
        """Set the file path of experiment's analysis result.
        Support object storage and file storage.
        """
        if isinstance(self.file, BaseFile):
            return

        task = self._label

        if self.run_options.collect_qubits is True:
            qubit_str = self.get_qubit_str()
        else:
            qubit_str = ""

        self.file = create_file(
            self.run_options.config,
            task,
            qubit_str,
            self.file_describe,
            self.dir_describe,
        )

        if self.run_options.parent_label is None:
            pyqlog.log(
                "EXP", f"{self} result path ({self.file.file_type}): {self.file.dirs}"
            )

        if self.experiment_options.record_text is True:
            text = f"{self.EXP_TYPE} | {type(self).__name__} | {self.record_id}\n\n"
            self.file.save_text(text, "records")

    def _save_curve_analysis_plot(self, save_mark: Optional[str] = None):
        """Save CurveAnalysis plot figure."""
        if self.analysis and self.analysis_options.is_plot and self.file:
            if hasattr(self.analysis, "drawer") and getattr(self.analysis, "drawer"):
                drawer = self.analysis.drawer

                png_name: str = str(self.experiment_data) or self._label
                if save_mark is not None:
                    png_name += f"-{save_mark}"

                if isinstance(drawer.figure, list):
                    for idx, fig in enumerate(drawer.figure):
                        self.file.save_figure(
                            fig, f"{png_name}_{idx}_(result)", close=True
                        )
                else:
                    self.file.save_figure(
                        drawer.figure, f"{png_name}(result)", close=True
                    )

                if self.experiment_options.record_text is True:
                    title_text = f"Title: {drawer.options.title} \n\n"
                    self.file.save_text(title_text, name="records")

    def _check_theta(self, theta_type: str):
        if theta_type == "Xpi":
            theta = np.pi
        elif theta_type == "Xpi/2":
            theta = np.pi / 2
        else:
            raise ExperimentOptionsError(
                self.label,
                msg="theta type only support Xpi and Xpi/2",
                key="theta_type",
                value=theta_type,
            )
        return theta

    def _check_options(self):
        """Check Options."""
        self.init_context_meta()
        if (
            self.EXP_TYPE != ExperimentType.BATCH
            and self.run_options.parallel is False
            and not self.record_meta.execute_meta.parent_id
        ):
            self.record_meta.execute_meta.is_root = True

    def _validate_options(self):
        """Validate Options."""
        self.experiment_options.validate_options()
        self.run_options.validate_options()
        self.analysis_options.validate_options()

        use_simulator = self.experiment_options.use_simulator
        simulator_data_path = self.experiment_options.simulator_data_path

        if use_simulator and simulator_data_path is not None:
            if isinstance(
                simulator_data_path, str
            ) and not simulator_data_path.endswith(".dat"):
                simulator_shape = self.experiment_options.simulator_shape
                paths = rocket_optimize(self.experiment_options.simulator_data_path)
                if simulator_shape is not None:
                    final_paths = []
                    index = 0
                    for shape in simulator_shape:
                        final_paths.append(paths[index : index + shape])
                        index += shape
                    paths = final_paths
                if paths:
                    self.set_run_options(simulator_data_path=paths)
            else:
                self.set_run_options(simulator_data_path=simulator_data_path)

        # if self.EXP_TYPE in [ExperimentType.TOP, ExperimentType.COMP] and self.run_options.x_data is None:
        #     raise ExperimentOptionsError(self, msg="No set x data, maybe experiment options set error!")

    def _put_info_to_queue(self, data: Optional[Dict] = None):
        """Put information data to data service.

        Args:
            data (Dict): Put to Queue data,

        """
        file_path = None

        if isinstance(self.file, BaseFile):
            file_path = self.file.dirs

        child_experiment = getattr(self, "child_experiment", None)
        if child_experiment:
            exp_type = "composite"
            child_experiment.paternal_id = self.id
        else:
            exp_type = "single"
            pass

        if data and isinstance(data, Dict):
            extra = data
        else:
            extra = {}

        def add_base_qubit():
            base_qubits = {}
            for qubit in self.qubits:
                base_qubits[qubit.name] = qubit.to_dict()
            for coupler in self.couplers:
                base_qubits[coupler.name] = coupler.to_dict()
            for qubit_pair in self.qubit_pairs:
                base_qubits[qubit_pair.name] = qubit_pair.to_dict()

            return base_qubits

        def add_compensates():
            compensates = {}
            for key, value in self.compensates.items():
                compensates[key.name] = value.to_dict()
            return compensates

        def add_dcm():
            ds = {}
            if self.discriminator:
                discriminators: List = []
                if isinstance(self.discriminator, IQdiscriminator):
                    discriminators = [self.discriminator]
                elif isinstance(self.discriminator, List):
                    discriminators = self.discriminator
                for dcm in discriminators:
                    ds[dcm.name] = dcm.to_dict()
            return ds

        extra.update(
            {
                "file_path": file_path,
                "context": {
                    "base_qubits": add_base_qubit(),
                    "pulse_compensates": add_compensates(),
                    "discriminators": add_dcm(),
                    "working_dc": self.working_dc,
                    "working_ac": dict(
                        [
                            (bit.name, [bit.z_flux_channel, bit.ac])
                            for bit in self.compensates
                        ]
                    ),
                    "ac_bias": self.ac_bias,
                    "options": self.cache_options(),
                    "crosstalk": self.run_options.crosstalk_dict,
                },
            }
        )
        add_exec_t = Thread(
            target=self.add_execute_exp_note,
            args=(exp_type, extra),
            daemon=True,
        )
        add_exec_t.start()

        if self.experiment_options.save_context is True and self.file:
            try:
                json_extra = json.dumps(
                    extra, ensure_ascii=False, indent=4, cls=PyQCatEncoder
                )
                self.file.save_text(
                    json_extra, name=f"{self._label}_context", prefix=".json"
                )
            except Exception as err:
                pyqlog.warning(f"{self._label} save context error: {err}")

    def set_parallel_state(self, state: int = 1, msg: str = "Ok", err=None):
        describe = f"Parallel {self._label} ({self.get_qubit_str()})"
        self.parallel_state = (state, describe, msg, err)

    async def collect_record_data(self):
        self.record_meta.finish(self, self.EXP_TYPE)
        if self.experiment_options.save_context:
            await AppDataCollector().send_execute_data_to_courier(
                record_data=self.record_meta.to_dict()
            )
        else:
            pyqlog.debug(f"Skip database write for {self._label} (save_context=False)")

    def init_context_meta(self):
        # collect experiment options
        self.record_meta.context_meta.experiment_options = (
            self.experiment_options.to_dict()
        )

        # collect analysis options
        self.record_meta.context_meta.analysis_options = self.analysis_options.to_dict()

        # collect context name
        self.record_meta.context_meta.context_name = self.run_options.context_name

        # collect unit map
        if self.run_options.unit_map:
            record_infos = {}
            for name, unit in self.run_options.unit_map.items():
                record_infos[name] = unit.name
            self.record_meta.context_meta.unit_map = record_infos

        parent_id = self.record_meta.execute_meta.parent_id
        parallel_id = self.record_meta.execute_meta.parallel_id

        if not parent_id:
            # collect physical unit
            physical_units_map = self.physical_units_map()
            if physical_units_map:
                self.record_meta.context_meta.physical_units = {
                    unit_name: deepcopy(unit_obj.to_dict())
                    for unit_name, unit_obj in physical_units_map.items()
                }

            # collect dcm
            if self.discriminator:
                dcms = (
                    self.discriminator
                    if isinstance(self.discriminator, List)
                    else [self.discriminator]
                )
                self.record_meta.context_meta.dcms = {
                    dcm.name: dcm.to_dict() for dcm in dcms
                }

            if not parallel_id:
                # collect pulse compensate
                if self.compensates:
                    self.record_meta.context_meta.compensates = {
                        unit_obj.name: compensate.to_dict()
                        for unit_obj, compensate in self.compensates.items()
                    }

                # collect working dc
                if self.working_dc:
                    self.record_meta.context_meta.working_dc = deepcopy(self.working_dc)

                # collect ac bias
                if self.ac_bias:
                    self.record_meta.context_meta.ac_bias = deepcopy(self.ac_bias)

                # collect crosstalk dict
                if self.run_options.crosstalk_dict:
                    self.record_meta.context_meta.crosstalk_dict = deepcopy(
                        self.run_options.crosstalk_dict
                    )

                # collect xy_crosstalk_dict
                if self.run_options.xy_crosstalk_dict:
                    self.record_meta.context_meta.xy_crosstalk_dict = deepcopy(
                        self.run_options.xy_crosstalk_dict
                    )

    def check_exp_is_done(self, open: bool = True):
        is_done = self.status.is_done()
        if not is_done and open is True:
            raise ChildExperimentError()
        return is_done
